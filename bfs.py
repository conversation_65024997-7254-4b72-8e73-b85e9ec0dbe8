from tkinter import *
import random
import time

maze_size = 10
maze = [[1 for _ in range(maze_size)] for _ in range(maze_size)]
rectangles = [[None for _ in range(maze_size)] for _ in range(maze_size)]
start = (0, 0)

root = Tk()
root.title("AI Cleaning Agent (DFS) – Auto Path & Dirt")
canvas = Canvas(root, width=maze_size * 40, height=maze_size * 40)
canvas.pack()

# Draw the grid
for i in range(maze_size):
    for j in range(maze_size):
        x1, y1 = j * 40, i * 40
        x2, y2 = x1 + 40, y1 + 40
        rectangles[i][j] = canvas.create_rectangle(x1, y1, x2, y2, fill="black", outline="white")

# DFS algorithm
def dfs(start, goal):
    stack = [(start, [start])]
    visited = set()
    while stack:
        (x, y), path = stack.pop()
        if (x, y) == goal:
            return path
        if (x, y) in visited:
            continue
        visited.add((x, y))
        for dx, dy in [(-1,0),(1,0),(0,-1),(0,1)]:
            nx, ny = x + dx, y + dy
            if 0 <= nx < maze_size and 0 <= ny < maze_size and maze[nx][ny] == 0:
                stack.append(((nx, ny), path + [(nx, ny)]))
    return None

# Find all dirt
def find_dirt_positions():
    return [(i, j) for i in range(maze_size) for j in range(maze_size)
            if maze[i][j] == 0 and canvas.itemcget(rectangles[i][j], 'fill') == "brown"]

# Randomly generate walkable path
def generate_random_path():
    global start
    for i in range(maze_size):
        for j in range(maze_size):
            if random.random() < 0.7:
                maze[i][j] = 0  # Walkable
                canvas.itemconfig(rectangles[i][j], fill="white")
            else:
                maze[i][j] = 1  # Wall
                canvas.itemconfig(rectangles[i][j], fill="black")
    # Pick a random walkable start
    walkables = [(i, j) for i in range(maze_size) for j in range(maze_size) if maze[i][j] == 0]
    if walkables:
        start = random.choice(walkables)
        sx, sy = start
        canvas.itemconfig(rectangles[sx][sy], fill="blue")

# Generate dirt randomly on reachable cells
def generate_random_dirt(count=15):
    reachable = [(i, j) for i in range(maze_size) for j in range(maze_size)
                 if maze[i][j] == 0 and (i, j) != start and dfs(start, (i, j)) is not None]
    random.shuffle(reachable)
    dirt = reachable[:count]
    for x, y in dirt:
        canvas.itemconfig(rectangles[x][y], fill="brown")

# Clean dirt using DFS
def clean_all_dirt():
    dirt_positions = find_dirt_positions()
    current_pos = start

    def clean_next():
        nonlocal current_pos, dirt_positions
        if not dirt_positions:
            return
        next_dirt = dirt_positions.pop(0)
        path = dfs(current_pos, next_dirt)
        if path:
            for pos in path[1:]:
                x, y = pos
                fill = canvas.itemcget(rectangles[x][y], "fill")
                if fill == "brown" or fill == "white":
                    canvas.itemconfig(rectangles[x][y], fill="green")
                root.update()
                time.sleep(0.1)
            current_pos = next_dirt
        root.after(100, clean_next)

    clean_next()

# Reset everything
def reset_maze():
    global maze
    maze = [[1 for _ in range(maze_size)] for _ in range(maze_size)]
    for i in range(maze_size):
        for j in range(maze_size):
            canvas.itemconfig(rectangles[i][j], fill="black")

# Buttons
btn_frame = Frame(root)
btn_frame.pack(pady=10)

Button(btn_frame, text="Generate Path + Dirt", command=lambda: [reset_maze(), generate_random_path(), generate_random_dirt()]).pack(side=LEFT, padx=5)
Button(btn_frame, text="Start Cleaning", command=clean_all_dirt).pack(side=LEFT, padx=5)
Button(btn_frame, text="Reset All", command=reset_maze).pack(side=LEFT, padx=5)

generate_random_path()
generate_random_dirt()
root.mainloop()
