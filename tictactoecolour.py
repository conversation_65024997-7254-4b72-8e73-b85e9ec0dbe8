import time
from colorama import Fore, Style, init

init(autoreset=True)  # Auto-reset color after each print

def print_board(board):
    for row in board:
        colored_row = []
        for cell in row:
            if cell == "X":
                colored_row.append(Fore.RED + cell)
            elif cell == "O":
                colored_row.append(Fore.BLUE + cell)
            else:
                colored_row.append(" ")
        print("|".join(colored_row))
        print("-" * 9)

def check_win(board, player):
    for i in range(3):
        if all(cell == player for cell in board[i]):
            return True
        if all(board[j][i] == player for j in range(3)):
            return True
    if all(board[i][i] == player for i in range(3)) or \
       all(board[i][2 - i] == player for i in range(3)):
        return True
    return False

def check_tie(board):
    return all(cell != " " for row in board for cell in row)

def tic_tac_toe():
    board = [[" " for _ in range(3)] for _ in range(3)]
    current_player = "X"

    while True:
        print_board(board)
        print(f"{Fore.GREEN}Player {Fore.RED if current_player == 'X' else Fore.BLUE}{current_player}{Fore.GREEN}'s turn.")

        start_time = time.time()

        try:
            row = int(input("Enter row (0-2): "))
            col = int(input("Enter column (0-2): "))
        except ValueError:
            print(Fore.YELLOW + "Invalid input. Please enter a number between 0 and 2.")
            continue

        if 0 <= row < 3 and 0 <= col < 3 and board[row][col] == " ":
            board[row][col] = current_player
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            print(Fore.MAGENTA + f"{current_player} took {duration} seconds to make a move.\n")

            if check_win(board, current_player):
                print_board(board)
                print(Fore.CYAN + f"🎉 Player {current_player} wins!")
                break

            if check_tie(board):
                print_board(board)
                print(Fore.YELLOW + "It's a tie!")
                break

            current_player = "O" if current_player == "X" else "X"
        else:
            print(Fore.YELLOW + "Invalid move. Try again.")

if __name__ == "__main__":
    tic_tac_toe()
