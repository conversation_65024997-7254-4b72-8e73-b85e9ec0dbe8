class ReflexVacuumAgent:
    """Two-Location Reflex Agent Implementation"""
    def __init__(self):
        self.location = 'A'  # initial location
        self.score = 0
    
    def act(self, location_status):
        location, status = location_status
        self.location = location
        
        if status == 'Dirty':
            self.score += 10
            return 'Suck'
        elif location == 'A':
            self.score -= 1
            return 'Right'
        else:
            self.score -= 1
            return 'Left'
    
    def get_score(self):
        return self.score

class TableDrivenVacuumAgent:
    """Table-Driven Agent for Two Locations"""
    def __init__(self):
        self.table = {
            ('A', 'Dirty'): 'Suck',
            ('A', 'Clean'): 'Right',
            ('B', 'Dirty'): 'Suck',
            ('B', 'Clean'): 'Left'
        }
        self.location = 'A'
        self.score = 0
    
    def act(self, location_status):
        action = self.table[location_status]
        self.location = location_status[0]
        
        if action == 'Suck':
            self.score += 10
        else:
            self.score -= 1
        
        return action
    
    def get_score(self):
        return self.score

class GridVacuumAgent:
    """Simple Grid-Based Vacuum Cleaner (2X2)"""
    def __init__(self):
        self.x = 0
        self.y = 0
        self.score = 0
        self.direction = 'right'  # initial direction
    
    def act(self, percept):
        (x, y), status = percept
        self.x, self.y = x, y
        
        if status == 'Dirty':
            self.score += 10
            return 'Suck'
        else:
            # Move in a simple pattern: right, down, left, up
            if self.direction == 'right':
                if self.x < 1:
                    self.score -= 1
                    return 'Right'
                else:
                    self.direction = 'down'
                    self.score -= 1
                    return 'Down'
            elif self.direction == 'down':
                if self.y < 1:
                    self.score -= 1
                    return 'Down'
                else:
                    self.direction = 'left'
                    self.score -= 1
                    return 'Left'
            elif self.direction == 'left':
                if self.x > 0:
                    self.score -= 1
                    return 'Left'
                else:
                    self.direction = 'up'
                    self.score -= 1
                    return 'Up'
            else:  # up
                if self.y > 0:
                    self.score -= 1
                    return 'Up'
                else:
                    self.direction = 'right'
                    self.score -= 1
                    return 'Right'
    
    def get_score(self):
        return self.score

def test_reflex_agent():
    """Test function for Reflex Agent"""
    print("\nTesting Two-Location Reflex Agent")
    print("="*40)
    agent = ReflexVacuumAgent()
    
    test_cases = [
        ('A', 'Dirty'),  # Should Suck
        ('A', 'Clean'),  # Should move Right
        ('B', 'Dirty'),  # Should Suck
        ('B', 'Clean')   # Should move Left
    ]
    
    for percept in test_cases:
        action = agent.act(percept)
        print(f"Percept: {percept} -> Action: {action}")
    
    print(f"Final Agent Score: {agent.get_score()}")

def test_table_driven_agent():
    """Test function for Table-Driven Agent"""
    print("\nTesting Table-Driven Agent for Two Locations")
    print("="*40)
    agent = TableDrivenVacuumAgent()
    
    test_cases = [
        ('A', 'Dirty'),  # Should Suck
        ('A', 'Clean'),  # Should move Right
        ('B', 'Dirty'),  # Should Suck
        ('B', 'Clean')   # Should move Left
    ]
    
    for percept in test_cases:
        action = agent.act(percept)
        print(f"Percept: {percept} -> Action: {action}")
    
    print(f"Final Agent Score: {agent.get_score()}")

def test_grid_agent():
    """Test function for Grid-Based Agent"""
    print("\nTesting Simple Grid-Based Vacuum Cleaner (2X2)")
    print("="*40)
    agent = GridVacuumAgent()
    
    test_cases = [
        ((0, 0), 'Dirty'),  # Should Suck
        ((0, 0), 'Clean'),  # Should move Right
        ((1, 0), 'Clean'),  # Should move Down
        ((1, 1), 'Dirty'),  # Should Suck
        ((1, 1), 'Clean'),  # Should move Left
        ((0, 1), 'Clean'),  # Should move Up
    ]
    
    for percept in test_cases:
        action = agent.act(percept)
        print(f"Percept: {percept} -> Action: {action}")
    
    print(f"Final Agent Score: {agent.get_score()}")

def main():
    """Main program menu"""
    while True:
        print("\nVacuum Cleaner Agent Simulator")
        print("="*30)
        print("1. Two-Location Reflex Agent")
        print("2. Table-Driven Agent for Two Locations")
        print("3. Simple Grid-Based Vacuum Cleaner (2X2)")
        print("4. Exit")
        
        choice = input("Enter your choice (1-4): ")
        
        if choice == '1':
            test_reflex_agent()
        elif choice == '2':
            test_table_driven_agent()
        elif choice == '3':
            test_grid_agent()
        elif choice == '4':
            print("Exiting program...")
            break
        else:
            print("Invalid choice. Please enter a number between 1 and 4.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()