import heapq

class Node:
    def __init__(self, position, parent=None):
        self.position = position  # (x, y)
        self.parent = parent
        self.g = 0  # Cost from start to current node
        self.h = 0  # Heuristic cost to goal
        self.f = 0  # Total cost (g + h)

    def __lt__(self, other):
        return self.f < other.f

def heuristic(a, b):
    # Manhattan distance
    return abs(a[0] - b[0]) + abs(a[1] - b[1])

def astar(grid, start, end):
    open_list = []
    closed_set = set()

    start_node = Node(start)
    end_node = Node(end)
    heapq.heappush(open_list, start_node)

    while open_list:
        current_node = heapq.heappop(open_list)
        if current_node.position == end_node.position:
            # Reconstruct path
            path = []
            while current_node:
                path.append(current_node.position)
                current_node = current_node.parent
            return path[::-1]  # Return reversed path

        closed_set.add(current_node.position)

        # Explore neighbors (4 directions)
        for dx, dy in [(-1,0), (1,0), (0,-1), (0,1)]:
            neighbor_pos = (current_node.position[0] + dx, current_node.position[1] + dy)

            # Check bounds
            if (0 <= neighbor_pos[0] < len(grid)) and (0 <= neighbor_pos[1] < len(grid[0])):
                # Check for wall or already visited
                if grid[neighbor_pos[0]][neighbor_pos[1]] == 1 or neighbor_pos in closed_set:
                    continue

                neighbor = Node(neighbor_pos, current_node)
                neighbor.g = current_node.g + 1
                neighbor.h = heuristic(neighbor_pos, end_node.position)
                neighbor.f = neighbor.g + neighbor.h

                # Check if this node is already in open_list with a better f
                if any(open_node.position == neighbor.position and open_node.f <= neighbor.f for open_node in open_list):
                    continue

                heapq.heappush(open_list, neighbor)

    return None  # No path found

# Example grid (0 = walkable, 1 = wall)
grid = [
    [0, 1, 0, 0, 0],
    [0, 1, 0, 1, 0],
    [0, 0, 0, 1, 0],
    [1, 1, 0, 1, 0],
    [0, 0, 0, 0, 0]
]

start = (0, 0)
end = (4, 4)

path = astar(grid, start, end)
if path:
    print("Path found:", path)
else:
    print("No path found.")
