class WumpusWorldAgent:
    def __init__(self):
        self.kb = set()  # Knowledge Base: stores known facts
        self.visited = set()
        self.safe = set()
        self.percepts = {}  # {(x,y): [breeze, stench]}

    def tell(self, location, percept):
        self.percepts[location] = percept
        self.kb.add(f"Visited({location})")
        self.visited.add(location)
        self.infer_safe(location, percept)

    def infer_safe(self, loc, percept):
        x, y = loc
        neighbors = self.get_neighbors(x, y)
        breeze, stench = percept

        if not breeze and not stench:
            for n in neighbors:
                self.kb.add(f"Safe({n})")
                self.safe.add(n)
        elif not breeze:
            for n in neighbors:
                self.kb.add(f"NoPit({n})")
                self.safe.add(n)
        elif not stench:
            for n in neighbors:
                self.kb.add(f"NoWumpus({n})")
                self.safe.add(n)

    def get_neighbors(self, x, y):
        directions = [(0,1), (1,0), (0,-1), (-1,0)]
        neighbors = []
        for dx, dy in directions:
            nx, ny = x+dx, y+dy
            if 0 <= nx < 4 and 0 <= ny < 4:
                neighbors.append((nx, ny))
        return neighbors

    def next_move(self, current_loc):
        for n in self.get_neighbors(*current_loc):
            if n not in self.visited and n in self.safe:
                return n
        return None

    def display_kb(self):
        print("\nKnowledge Base:")
        for fact in sorted(self.kb):
            print(fact)


# -------- Simulate Agent ----------
agent = WumpusWorldAgent()

# Agent starts at (0,0)
agent.tell((0,0), percept=[False, False])  # No breeze, no stench

# Agent moves to (1,0)
agent.tell((1,0), percept=[True, False])   # Breeze, no stench

# Agent moves to (0,1)
agent.tell((0,1), percept=[False, True])   # No breeze, stench

agent.display_kb()

# Choose next move
current_position = (0,1)
next_pos = agent.next_move(current_position)
print(f"\nNext move from {current_position}: {next_pos}")
